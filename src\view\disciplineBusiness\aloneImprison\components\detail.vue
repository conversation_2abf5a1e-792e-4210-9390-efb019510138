<template>
  <div>
	<div class="bsp-base-form">
		<div class="bas-base-content">
			<div class="keyperson-content">
				<div class="keyperson-content-left">
					<div class="selectPerson">
						<personnel-selector
							:value="formData.jgrybm"
							mode="detail"
							title="被监管人员"
							:show-case-info="true"
						/>
					</div>

					<div class="personList">
						<Tabs value="name1">
							<TabPane label="单独关押记录" name="name1">
								<alonelmprisonRecords :takeNotes="takeNotes" :stepList="stepFeedbackList" :current="current" :stepType="stepType"></alonelmprisonRecords>
							</TabPane>
						</Tabs>
					</div>
				</div>
				<div class="keyperson-content-right">
					<div class="keyperson-content-rightTop">
						<p class="sys-sub-title">单独关押详情</p>
						<div class="ryxx-row">
							<Row>
								<Col span="4">单独关押原因</Col>
								<Col span="8">{{ aloneImprisonInfo.registerReasonName }}</Col>
								<Col span="4">具体原因</Col>
								<Col span="8">{{ aloneImprisonInfo.specificReason }}</Col>
							</Row>
							<Row>
								<Col span="4">原监室号</Col>
								<Col span="8">{{ aloneImprisonInfo.oldRoomName }}</Col>
								<Col span="4">新监室号</Col>
								<Col span="8">{{ aloneImprisonInfo.newRoomName }}</Col>
							</Row>
							<Row>
								<Col span="4">是否关联惩罚</Col>
								<Col span="8">{{ aloneImprisonInfo.isAssociatedPunishment === 1 ? '是' : '否' }}</Col>
								<Col span="4">惩罚内容</Col>
								<Col span="8">{{ aloneImprisonInfo.punishmentMeasuresName }}</Col>
							</Row>
							<Row>
								<Col span="4">申请人</Col>
								<Col span="8">{{ aloneImprisonInfo.addUserName }}</Col>
								<Col span="4">申请时间</Col>
								<Col span="8">{{ aloneImprisonInfo.addTime }}</Col>
							</Row>
							<Row v-if="stepType == 'detail'">
								<Col span="4">审批结果</Col>
								<Col span="8">{{ aloneImprisonInfo.approvalResult == '1' ? '同意' : '不同意' }}</Col>
							</Row>
							<Row v-if="stepType == 'detail'">
								<Col span="4">审批意见</Col>
								<Col span="20">{{ aloneImprisonInfo.approvalComments }}</Col>
							</Row>
						</div>
						<div class="operate" v-if="stepType == 'approval'">
							<p class="sys-sub-title">单独关押审批</p>
							<div>
								<Form ref="aloneImprisonInfo" :model="aloneImprisonInfo" :label-width="120" :label-colon="true" class="base-form-container">
									<Row>
										<Col span="24">
											<FormItem label="审批结果" prop="approvalResult" :rules="[{ trigger: 'blur,change', message: '请选择审批结果', required: true }]" style="width: 100%;">
												<RadioGroup v-model="aloneImprisonInfo.approvalResult">
													<Radio label="1">同意</Radio>
													<Radio label="0">不同意</Radio>
												</RadioGroup>
											</FormItem>
										</Col>
										<Col span="24">
											<FormItem label="审批意见" prop="approvalComments" style="width: 100%;">
												<Input v-model="aloneImprisonInfo.approvalComments" maxlength="2000"
													type="textarea" show-word-limit :autosize="{ minRows: 4, maxRows: 6 }"
													placeholder="请填写" />
											</FormItem>
										</Col>
									</Row>
								</Form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="bsp-base-fotter">
			<Button @click="onCancel" style="margin-right: 10px">取消</Button>
			<Button @click="onSubmit" v-if="stepType === 'approval'" :loading="loading" type="primary">提交</Button>
		</div>
	</div>
  </div>
</template>

<script>
import alonelmprisonRecords from './alonelmprisonRecords.vue'
import personnelSelector from "@/components/personnel-selector"
export default {
	components: {
		personnelSelector,
		alonelmprisonRecords
	},
	props: {
		stepType: String,
		formData: {
			type: Object,
			default: {}
		},
	},
	data() {
		return {
			takeNotes: '单独关押记录',
			stepFeedbackList: [],
			current: 1,
			orgType: this.$store.state.common.orgType,
			loading: false,
			aloneImprisonInfo: {},
		}
	},

	methods: {
		onCancel() {
			this.$emit('on_show_table')
		},
		onSubmit() {
			this.loading = true
			this.$refs['aloneImprisonInfo'].validate((valid) => {
				console.log(valid)
				if(!valid) {
					this.loading = false
					this.$Message.error('请填写完整!!')
					return
				}
				try {
					this.$store.dispatch('authPostRequest',{
						url: this.$path.aloneImprison_approval,
						params: {
							id: this.aloneImprisonInfo.id,
							approvalResult: this.aloneImprisonInfo.approvalResult,
							takeNoteapprovalCommentss: this.aloneImprisonInfo.approvalComments
						}
					}).then(res => {
						if(res.success) {
							console.log(res);
							this.loading = false
							this.$Message.success('操作成功！')
							this.$nextTick(() => {
								this.$emit('on_show_table')
							})
						} else {
							this.loading = false
							this.$Modal.error({
								title: '温馨提示',
								content: res.msg || '操作失败！'
							})
						}
					}).catch(err => {
						console.log(err)
						this.loading = false
							this.$Modal.error({
								title: '温馨提示',
								content: err || '操作失败！'
							})
					})
				} catch(e) {
					console.log(e)
					this.$Modal.error({
						title: '温馨提示',
						content: e || '操作失败！'
					})
				}
			})
		},
		getAloneImprison(id) {
			this.$store.dispatch('authGetRequest', {
				url: this.$path.aloneImprison_get_by_id,
				params: {
					id: id
				}
			}).then(res => {
				if(res.success) {
					this.aloneImprisonInfo = res.data
				} else {
					this.$Modal.error({
						title: '温馨提示',
						content: res.msg || '操作失败!'
					})
				}
			})
		},
		getAloneImprisonDetail(id) {
			this.$store.dispatch('authGetRequest', {
				url: this.$path.aloneImprison_get_by_id,
				params: {
					id: id
				}
			}).then(res => {
				if(res.success) {
					this.aloneImprisonInfo = res.data
				} else {
					this.$Modal.error({
						title: '温馨提示',
						content: res.msg || '操作失败!'
					})
				}
			})
		},
	  getAloneImprisonHistory(jgrybm) {
			this.$store.dispatch('authGetRequest',{
				url: this.$path.aloneImprison_get_by_jgrybm,
				params: {
					jgrybm: jgrybm
				}
			}).then(res => {
				if(res.data.list) {
					this.stepFeedbackList = res.data.list
				}
			})
		},

	},
	created() {
		this.getAloneImprison(this.formData.id)
		this.getAloneImprisonHistory(this.formData.jgrybm)
	},
	mounted() {
		console.log(this.stepType,'-detail');
	}
}
</script>

<style lang="less" scoped>
.keyperson-content{
	width: 100%;
	height: 100%;
	display: flex;
	.keyperson-content-left{
		width: 30%;
		// height: 100%;
		min-height: 680px;
		border-right: 1px solid #f1efef;
		padding: 12px;
		box-sizing: border-box;
		// background: orangered;
		.selectPerson{
			margin-bottom: 15px;
		}
	}
	.keyperson-content-right{
		position: relative;
		flex: 1;
		// height: 100%;
		min-height: 680px;
		padding: 20px;
		overflow: hidden;
		.keyperson-content-rightTop{
			color: #415060;
			min-height: 25rem;
			.title{
				position: relative;
				font-size: 16px;
				// color: #385572;
				font-weight: 500;
				line-height: 45px;
				height: 45px;
				padding-left: 15px;
				margin-left: 10px;
				margin-bottom: 10px;
			}
			.title::before {
				position: absolute;
				content: "";
				background-color: #087eff;
				width: 4px;
				height: 20px;
				left: 0;
				top: 50%;
				margin-top: -10px;
				// -webkit-border-radius: 3px;
				// -moz-border-radius: 3px;
				// border-radius: 3px;
				}
			}
		
			.operatBtn{
				width: 100%;
				height: 50px;
				line-height: 50px;
				text-align: right;
				background: #fff;
				position: absolute;
				bottom: 0px;
				right: 15px;
				z-index: 999;
			}
	}
}
.bsp-base-form .ivu-form .ivu-form-item-label{
	color: #b9c0c8;
}
div.v-selectpage div.sp-input-container div.sp-clear i{
	display: none;
}
.ryxx-row .ivu-col{
    font-size: 16px;
    text-align: center;
    border:1px solid #fff;
    line-height: 39px;
}
.ryxx-row .ivu-col:nth-of-type(2n){
   background: #f9f9f9;
}
.ryxx-row .ivu-col:nth-of-type(2n+1){
   background: #e4eefc;
}
.ryxx-rows .ivu-col{
	font-size: 16px;
    border:1px solid #fff;
    line-height: 39px;
}
.ryxx-rows .ivu-col:nth-of-type(2n){
   background: #f9f9f9;
   text-align: left;
   padding-left: 10px;
}
.ryxx-rows .ivu-col:nth-of-type(2n+1){
   background: #e4eefc;
   text-align: center;
}
/deep/.ivu-steps .ivu-steps-head-inner > .ivu-steps-icon.ivu-icon{
	font-size: 16px !important;
}
.ivu-steps-vertical{
	height: 300px !important;
	overflow: auto !important;
}
/deep/.ivu-steps-item.ivu-steps-status-process .ivu-steps-content{
	color: #415060 !important;
}
/deep/.ivu-steps-item.ivu-steps-status-process .ivu-steps-title{
	color: #415060 !important;
}
.disabled {
  color: #999;
  cursor: not-allowed;
  pointer-events: none; /* 禁止所有鼠标事件 */
}
.checkbox-row {
  display: flex;
  margin-bottom: 12px;
}
.checkbox-item {
  flex: 0 0 20%;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>