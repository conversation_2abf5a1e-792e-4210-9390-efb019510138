<!-- 监室信息管理 -->
<template>
  <div>
    <div class="bsp-base-form">
      <div class="bsp-base-content" v-if="showFormCompnent">
        <div class="dbxx-wrap DataGrid-BOX">
          <div class="dbxx-wrap-search fm-content-wrap">
            <Form ref="formData" :model="searchItem" :label-colon="true" class="formbox" label-position="right"
                  :label-width="110">
              <Row>
                <Col span="8">
                  <FormItem label="监区">
                    <Select clearable filterable @on-select="orgChange" v-model="searchItem.area_code" @on-change="areaChange"
                            @on-clear="areaClear">
                      <Option v-for="item in orgCodeList" :value="item.areaCode" :key="item.id">{{ item.areaName }}</Option>
                    </Select>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="监室名称">
                    <Select v-model="searchItem.room_code" clearable @on-select="areaSelect" @on-change="roomChange"
                            @on-clear="roomClear">
                      <Option v-for="item in areaList" :value="item.roomCode" :key="item.roomCode">{{ item.roomName }}</Option>
                    </Select>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="监室类型">
                    <s-dicgrid @values="jslxValues" v-model="searchItem.room_type" :isSearch="false" style="width: 100%;"
                               dicName="ZD_ZXGJSLX"/>
                  </FormItem>
                </Col>
              </Row>
            </Form>
            <div style="width: 100%;text-align: center;">
              <Button @click="reset">重 置</button> &nbsp;&nbsp;&nbsp;<Button @click="search" type="primary">查 询
            </button>
            </div>
          </div>
          <div class="action-btn">
            <div class="action-btn-left">
              <Button type="primary" style="width: 100px;" @click="addRoomEvent">新增</Button>
              <Button type="primary" ghost style="width: 100px; border: 1px solid #2d8cf0;"
                      @click.native="triggerFileInput">导入
              </Button>
              <input
                type="file"
                ref="fileInput"
                style="display: none"
                accept=".xls,.xlsx"
                @change="handleFileChange"
              >
              <Button type="primary" ghost style="width: 100px; border: 1px solid #2d8cf0;" @click.native="temEvent">
                导出
              </Button>
              <Button type="primary" ghost style="width: 100px; border: 1px solid #2d8cf0;" @click="downloadTemplate">
                模板导出
              </Button>
            </div>
            <div style="display: flex;">
              <div class="card-method-change first-card" @click="showCard('card')"
                   :class="{'is-active':showMethod==='card'}">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16">
                  <g transform="translate(-1782.292 -8)">
                    <path
                      class="a" d="M1785,24V17h7v7Zm0-16h7v7h-7Zm-9,9h7v7h-7Zm0-9h7v7h-7Z"
                      transform="translate(6.292)"
                      :style="{
                          fill:showMethod==='card'?'#317ff5':'#7A90CC',
                        'fill-rule':'evenodd'
                        }"
                    />
                  </g>
                </svg>
              </div>
              <div class="card-method-change" @click="showCard('table')" :class="{'is-active':showMethod==='table'}">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16">
                  <path
                    class="a" d="M1829,23V21h11v2Zm0-8h11v2h-11Zm0-6h11v2h-11Zm-5,11h4v4h-4Zm0-6h4v4h-4Zm0-6h4v4h-4Z"
                    transform="translate(-1824 -8)"
                    :style="{
                        fill:showMethod==='table'?'#317ff5':'#7A90CC',
                        'fill-rule':'evenodd'
                      }"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div class="fm-content-list" v-if="showMethod == 'table'">
            <div class="dbxx-wrap-table" style="height: 80%; padding: 0 10px; width: 100%;">
              <!-- <Table border :columns="columns" :data="dataTable" height="543" width="100%" :loading="tableLoading"
                     @on-selection-change="handleSelectionChange">
                <template slot-scope="{ row, index }" slot="action">
                  <Button type="primary" @click.native="detailYp(row,index)" :loading="loadingDetail[index]">详情
                  </Button>
                  <Button type="primary" @click.native="editYp(row,index)" :loading="loadingStates[index]">编辑</Button>
                  <Button type="error" @click.native="deleteYp(row,index)" :loading="loadingDelete[index]"
                          style="margin-right: 0px !important;">删除
                  </Button>
                </template>
              </Table> -->

              <rs-DataGrid ref="grid" :funcMark="funcMark" v-if="funcMark" :params="searchItem">
                <!--详情操作-->
                <template v-slot:room:list:detail="{ oper, row }">
                  <Button type="primary" class="row-button" @click="detailYp(row, index)"> {{ oper.name }} </Button>
                </template>
                <!--编辑操作-->
                <template v-slot:room:list:edit="{ oper, row }">
                  <Button type="primary" class="row-button" @click="editYp(row, index)"> {{ oper.name }} </Button>
                </template>
                <!--删除操作-->
                <template v-slot:room:list:del="{ oper, row }">
                  <Button type="error" class="row-button" @click="deleteYp(row, index)"> {{ oper.name }} </Button>
                </template>
              </rs-DataGrid>
              
            </div>
            <!-- <div class="pageWrap">
              <Page :total="total" class="pageWrap" size="small" show-elevator show-sizer show-total
                    :page-size="page.pageSize" @on-prev="getNo" @on-next="getNo" :current="page.pageNo"
                    @on-change="getNo" @on-page-size-change="getSize"/>
            </div> -->
          </div>
          <div v-if="showMethod == 'card'">
            <div v-if="groupsCards.length > 0">
              <div v-for="groupItem in groupsCards">
                <div class="fm-content-card-title">
                  <span>{{ groupItem.area_name }}（{{ groupItem.total }}间）</span>
                </div>
                <div class="fm-content-card" style="min-height: 410px;padding: 10px 0px 10px 30px; width: 100%;">
                  <div v-for="item in groupItem.rooms" :key="item.order" class="fm-content-card-item">
                    <div class="card-item-top">
                      <img v-if="item.room_sex == '1' || item.room_sex == '5'" src="@/assets/icons/man.svg"
                           class="roomSex" alt=""> <!-- 男 -->
                      <img v-else-if="item.room_sex == '2' || item.room_sex == '6'" src="@/assets/icons/woman.svg"
                           class="roomSex" alt=""> <!-- 女 -->
                      <img v-else src="@/assets/icons/无性别监室.svg" class="roomSex" alt=""> <!-- 未知 -->
                      <div>
                        <h4 style="color: #000; cursor: pointer;" @click="detailYp(item, item.order)"
                            class="hover-underline">
                          {{ item.room_name ? item.room_name : '-' }}</h4>
                        <p class="card-item-top-info">
                          <img src="@/assets/icons/fl-renyuan.svg" style="width: 16px; height: 18px;" alt="">
                          <span style="font-size: 14px; margin: 0px 5px;">{{ item.num }}</span>
                          <span v-if="isSponsor(item, userId)" class="tabs">主管</span>
                          <span v-else-if="isAssist(item, userId)" class="tabs">协管</span>
                          <span class="tabs-room-type" :style="{ backgroundColor: getRoomTypeStyle(item)}">{{
                              item.room_typeName
                            }} </span>
                        </p>
                      </div>
                    </div>
                    <div class="card-item-bottom">
                      <!-- 操作菜单容器 -->
                      <div class="action-menu-container">
                        <img
                          src="@/assets/icons/gengduo.svg"
                          @click.stop="actionEvent(item, item.order)"
                          class="action-menu-trigger"
                          alt="更多操作"
                        >
                        <div v-if="activeIndex === item.order" class="action-menu">
                          <div class="menu-item" style="color: rgb(131,188,254);"
                               @click.stop="editYp(item, item.order)"><img
                            src="@/assets/icons/bianji.svg" alt="图标" width="14" height="14"/>&nbsp;编辑
                          </div>
                          <div class="menu-item" style="color: rgb(216,30,6);" @click.stop="deleteYp(item, item.order)">
                            <img
                              src="@/assets/icons/a-shanchu1.svg" alt="图标" width="14" height="14"/>&nbsp;删除
                          </div>
                        </div>
                      </div>
                      <!-- 其他底部内容 -->
                      <Tooltip placement="top">
                        <p class="text-ellipsis">主管管教：{{ item.sponsor || '-' }}</p>
                        <template #content>
                          <p style="white-space: wrap;">{{ item.sponsor }}</p>
                        </template>
                      </Tooltip>
                      <div class="assist-container">
                        <Tooltip placement="top">
                          <p class="text-ellipsis">协管管教：{{ item.assist || '-' }}</p>
                          <template #content>
                            <p style="white-space: wrap;">{{ item.assist }}</p>
                          </template>
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <noData v-else/>
          </div>
        </div>
      </div>
      <div v-if="!showFormCompnent" style="height: 100%">
        <component v-bind:is='component' :isPs="true" @on_show_table="on_show_table" :syncAction="action"
                   :rowData="rowData" :modalTitle="modalTitle"></component>
      </div>
    </div>
  </div>
</template>

<script>
import {getToken} from '@/libs/util'
import detail from './components/detail.vue'
import roomDetail from './components/roomDetail.vue'
import Cookies from "js-cookie";
import axios from 'axios';
import noData from "@/components/bsp-empty/index.vue";

export default {
  components: {
    detail,
    roomDetail,
    noData
  },
  data() {
    return {
      funcMark:'room:list',
      cancelBtn: false,
      showData: false,
      tableLoading: false,
      // columns: [
      //   {
      //     type: 'index',
      //     width: 60,
      //     align: 'center'
      //   },
      //   {
      //     type: 'selection',
      //     width: 60,
      //     align: 'center'
      //   },
      //   {title: '监室名称', key: 'room_name', align: 'center', width: '140', tooltip: true},
      //   {title: '所属监区', key: 'area_name', align: 'center', width: '140', tooltip: true},
      //   {title: '监区编码', key: 'area_id', align: 'center', width: '160', tooltip: true},
      //   {
      //     title: '监室类型',
      //     align: 'center',
      //     width: 120,
      //     tooltip: true,
      //     key: 'room_typeName'
      //   },
      //   // { title: '是否已读',
      //   //   key: 'isRead',
      //   //   align: 'center',
      //   //   tooltip: true,
      //   //   render: (h, { row }) => {
      //   //     let color = row.isRead === '1' ? 'primary' : 'warning'
      //   //     let isSingleHtml = row.isRead === '1' ? '已读' : '未读'
      //   //     return h('Tag', {
      //   //       props: {
      //   //         color: color
      //   //       }
      //   //     }, isSingleHtml)
      //   //   }
      //   // },
      //   // { title: '是否已处理',
      //   //   key: 'isProc',
      //   //   align: 'center',
      //   //   tooltip: true,
      //   //   render: (h, { row }) => {
      //   //     let color = row.isProc === '1' ? 'geekblue' : 'volcano'
      //   //     let isSingleHtml = row.isProc === '1' ? '已处理' : '未处理'
      //   //     return h('Tag', {
      //   //       props: {
      //   //         color: color
      //   //       }
      //   //     }, isSingleHtml)
      //   //   }
      //   // },
      //   {
      //     title: '关押量',
      //     key: 'imprisonment_amount',
      //     align: 'center',
      //     width:110
      //   },
      //   {
      //     title: ' 监室面积（㎡）',
      //     key: 'room_area',
      //     align: 'center',
      //     width:150
      //   },
      //   {
      //     title: '主管民警',
      //     key: 'sponsor',
      //     align: 'center',
      //     width: 150,
      //     tooltip: true
      //   },
      //   {
      //     title: '协管民警',
      //     key: 'assist',
      //     align: 'center',
      //     width: 150,
      //     tooltip: true
      //   },
      //   {
      //     title: '创建时间',
      //     key: 'add_time',
      //     align: 'center',
      //     width: 190,
      //     tooltip: true
      //   },
      //   {
      //     title: '更新时间',
      //     key: 'update_time',
      //     align: 'center',
      //     width: 150,
      //     tooltip: true
      //   },
      //   {
      //     title: '操作',
      //     slot: 'action',
      //     width: 273,
      //     align: 'center',
      //     fixed: 'right',
      //   }
      // ],
      dataTable: [],
      groupsCards: [],
      page: {
        pageNo: 1,
        pageSize: 10,
      },
      searchItem: {
        org_code: this.$store.state.common.orgCode
      },
      total: 0,
      showFormCompnent: true,
      action: 'add',
      rowData: {},
      component: null,
      modalTitle: '监事新增',
      showMethod: 'table',
      // orgCode: this.$store.state.common.orgCode
      orgCodeList: [],
      areaList: [],
      isShow: false,
      activeIndex: null,
      userId: Cookies.get("bsp_bus_user_id"),
      discipline: '',
      loadingStates: [],
      loadingDetail: [],
      loadingDelete: [],
      selectedRows: []
    }
  },
  watch: {
    'searchItem.area_code': {
      handler(n, o) {
        console.log(n, o);
        if (!n) {
          this.areaList = []
          this.searchItem.room_code = ''
        }
      },
      // immediate: true,
      // deep: true,
    }
  },
  mounted() {
    this.getTableData()
    this.getAreaByOrgCode()
  },
  methods: {
    isSponsor(item, userId) {
      // 主管只有一个，直接比较
      return String(item.sponsor_ids) === String(userId);
    },
    isAssist(item, userId) {
      if (!item.assist_ids) return false;
      // 协管多个，用逗号分隔，转成数组判断
      const assistArr = item.assist_ids.split(',').map(id => id.trim());
      return assistArr.includes(String(userId));
    },
    reset() {
      this.searchItem={}

      this.$refs.formData.resetFields();

      this.funcMark=''
      if(this.showMethod=='card'){
       this.getTableData()
      }else{
        setTimeout(()=>{
        this.funcMark='room:list'
        },100)
      }
    },
    getNo(pageNo) {
      this.$set(this.page, 'pageNo', pageNo)
      this.getTableData()
    },
    getSize(pageSize) {
      this.$set(this.page, 'pageSize', pageSize)
      this.getTableData()
    },
    search() {
      this.funcMark=''
      if(this.showMethod=='card'){
       this.getTableData()
      }else{
        setTimeout(()=>{
        this.funcMark='room:list'
        },100)
      }
    },
    getTableData() {
      let condis=[]
      if (this.searchItem && this.searchItem.area_name && this.searchItem.area_name != '') {
        condis.push({
          name: 'area_name',
          op: 'like',
          value: this.removeAllSpaces(this.searchItem.area_name),
          valueType: 'string'
        })
      }
      if (this.searchItem && this.searchItem.room_name && this.searchItem.room_name != '') {
        condis.push({
          name: 'room_name',
          op: 'like',
          value: this.removeAllSpaces(this.searchItem.room_name),
          valueType: 'string'
        })
      }
      if (this.searchItem && this.searchItem.room_type && this.searchItem.room_type != '') {
        condis.push({
          name: 'room_type',
          op: '=',
          value: this.searchItem.room_type,
          valueType: 'string'
        })
      }
      if (condis) {
        const hasOrgCode = condis.some(i => i.name == 'org_code')
        if (!hasOrgCode) {
          condis.push({
            name: 'org_code',
            op: '=',
            value: this.$store.state.common.orgCode,
            valueType: 'string'
          })
        }
      }
      console.log('condis', condis);
      let params = {
        // access_token: getToken(),
        modelId: this.globalAppCode + ':room:list',
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
        browsePerm: true,
        condis: JSON.stringify(condis),
        sortName: 'add_time',
        sortType: 'desc'
      }

      if (this.showMethod === 'card') {
        params.pageNo = 1
        params.pageSize = 100000
      }

      this.$store.dispatch('postRequest', {
        url: this.$path.get_query_grid,
        params: params
      }).then(res => {
        if (res.success) {
          // this.dataTable = res.rows
          // this.total = res.total
          this.$set(this,'dataTable', res.rows)
          this.$set(this,'total', res.total)
          if (this.total === 0) {
            // this.dataTable = []
            this.$set(this,'dataTable',[])
          }
          if (this.showMethod === 'card') {
            this.groupByAreaId()
          }
        }
      })
    },
    removeAllSpaces(str) {
      if (!str) return ''
      return str.replace(/\s+/g, '')
    },
    getRoomTypeStyle(item) {
      if (item.room_sex === '1' || item.room_sex === '5') {
        return "#5db2fd"
      }
      if (item.room_sex === '2' || item.room_sex === '6') {
        return "#f97aa7"
      }
      return "#8ba8bc"
    },
    // 按area_id分组数据
    groupByAreaId() {
      this.assignGlobalSerialNumbers();
      const groups = {};
      // 遍历所有监室数据
      this.dataTable.forEach(room => {
        const {area_id} = room;
        // 如果该area_id还没有分组，则创建一个新分组
        if (!groups[area_id]) {
          groups[area_id] = {
            area_id,
            area_name: room.area_name,
            rooms: [],
            total: 0
          };
        }
        // 将当前监室添加到对应分组
        groups[area_id].rooms.push(room);
        groups[area_id].total += 1;
      });
      // 将对象转换为数组
      this.groupsCards = Object.values(groups);
      console.log(this.groupsCards)
    },
    assignGlobalSerialNumbers() {
      // 确保序号生成从1开始
      let serialNumber = 1;
      // 为每个监室分配全局唯一序号
      this.dataTable.forEach(room => {
        room.order = serialNumber++;
      });
    },
    roomValues(data) {
    },
    jslxValues(data) {

    },
    on_show_table(isRefreshTable) {
      this.showFormCompnent = true
      this.component = null
      // if (isRefreshTable) {
      this.getTableData()
      // }
    },
    showCard(name) {
      this.showMethod = name
      if (this.showMethod === 'card') {
        this.getTableData()
      }
    },
    detailYp(row, index) {
      this.$set(this.loadingDetail, index, true);
      this.$store.dispatch('getRequest', {
        url: this.$path.bsp_pam_infoManage_get,
        params: {
          roomCode: row.room_code,
          orgCode: row.org_code
        }
      }).then(res => {
        if (res.success) {
          console.log(res, 'res');
          this.action = 'detail'
          this.rowData = res.data
          this.showFormCompnent = false
          this.component = 'roomDetail'
          this.modalTitle = '监室详情'
          this.$set(this.loadingDetail, index, false);
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败!'
          })
          this.$set(this.loadingDetail, index, false);
        }
      })
    },
    editYp(row, index) {
      this.$set(this.loadingStates, index, true);
      this.$store.dispatch('getRequest', {
        url: this.$path.bsp_pam_infoManage_get,
        params: {
          orgCode: row.org_code,
          roomCode: row.room_code
        },
      }).then(res => {
        if (res.success) {
          console.log(res, 'res');
          this.action = 'edit'
          this.rowData = res.data
          this.showFormCompnent = false
          this.component = 'detail'
          this.modalTitle = '编辑监室信息'
          this.$set(this.loadingStates, index, false);
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败!'
          })
          this.$set(this.loadingStates, index, false);
        }
      })
    },
    actionEvent(item, index) {
      event.stopPropagation();
      console.log("-----------1")

      this.activeIndex = this.activeIndex === index ? null : index;

      // 点击其他地方关闭菜单
      if (this.activeIndex === index) {
        setTimeout(() => {
          const handler = (e) => {
            if (!e.target.closest('.action-menu-container')) {
              this.activeIndex = null;
              document.removeEventListener('click', handler);
            }
          };
          document.addEventListener('click', handler);
        }, 0);
      }
    },

    handleAction(type, item) {
      console.log(type, item);
      // 处理菜单项点击
      switch (type) {
        case 'delete':
          // 删除逻辑
          break;
        case 'edit':
          // 编辑逻辑
          break;
      }
      this.activeIndex = null; // 操作后关闭菜单
    },
    deleteYp(row, index) {
      console.log(row);
      this.$set(this.loadingDelete, index, true);
      if (row.num > 0) {
        this.$Modal.error({
          title: '温馨提示',
          content: '当前监室内已有在押人员，不允许删除!'
        })
        this.$set(this.loadingDelete, index, false);
        return;
      }
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.$store.dispatch('getRequest', {
            url: this.$path.bsp_pam_infoManage_delete,
            params: {
              ids: row.id
            }
          }).then(res => {
            if (res.success) {
              this.$Message.success('操作成功！')
              // this.$Modal.success({
              //   title: '温馨提示',
              //   content: '操作成功!'
              // })
              this.$set(this.loadingDelete, index, false);
              this.getTableData()
            } else {
              this.$Modal.error({
                title: '温馨提示',
                content: res.msg || '操作失败!'
              })
              this.$set(this.loadingDelete, index, false);
            }
          })
        }
      })

      this.$set(this.loadingDelete, index, false);
    },
    getAreaByOrgCode() {
      this.$store.dispatch('getRequest', {
        url: '/acp-com/base/area/getAreaByOrgCode',
        params: {
          orgCode: this.searchItem.org_code
        }
      }).then(res => {
        if (res.success) {
          this.orgCodeList = res.data
        }
      })
    },
    orgChange(data) {
      console.log('data', data)
      if (data) {
        this.searchItem.area_name = data.label
        this.getAreaList(data.value)
      }
    },
    areaClear() {
      this.areaCode = ''
    },
    areaSelect(data) {
      if (data) {
        this.searchItem.room_name = data.label
      }
    },
    roomClear() {
      this.searchItem.room_code = ''
    },
    getAreaList(areaId) {
      this.$store.dispatch('authPostRequest', {
        url: '/acp-com/base/pm/areaPrisonRoom/list',
        params: {
          orgCode: this.searchItem.org_code,
          areaId
        }
      }).then(res => {
        console.log(res, 'areaid');
        if (res.success) {
          this.areaList = res.data
        }
      })
    },
    areaChange(data) {
      if (!data) {
        this.searchItem.area_name = ''
      }
    },
    roomChange(data) {
      if (!data) {
        this.searchItem.room_name = ''
      }
    },
    addRoomEvent() {
      this.action = 'add'
      this.showFormCompnent = false
      this.component = 'detail'
      this.modalTitle = '新增监室'
    },
    downloadTemplate() {
      const link = document.createElement('a');
      link.href = '/downloads/监室导入模板.xls';
      link.download = '监室导入模板.xls';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    getroomDetail(item) {
      console.log('详情', item);
    },
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    handleFileChange(event) {
      const file = event.target.files[0];
      if (!file) return;

      const allowedExtensions = ['xls', 'xlsx'];
      const extension = file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(extension)) {
        this.$message.error('只能上传 .xls 或 .xlsx 文件');
        return;
      }

      this.uploadFile(file);
    },

    async uploadFile(file) {
      console.log(file, 'file');
      const formData = new FormData();
      formData.append('file', file);
      formData.append('orgCode', this.searchItem.org_code)
      console.log(formData, 'formData');

      try {
        const res = await axios.post(
          this.$path.bsp_pam_infoManage_importExcel,
          formData,
          {
            headers: {
              'Authorization': `Bearer ${getToken()}`,
            },
          }
        );
        // console.log(res,'res222');
        // this.on_show_table()
        // this.$Message.success(res.data.data)
        if (res.data.success) {
          this.on_show_table();
          this.$Message.success(res.data.data || '上传成功');
          return res.data;  // 返回成功数据
        } else {
          // 接口返回失败，抛出错误信息
          this.$Modal.error({
            title: '温馨提示',
            content: res.data.msg || '上传失败，接口返回错误'
          });
        }

      } catch (error) {
        this.$Message.error('文件上传失败');
      } finally {
        this.$refs.fileInput.value = '';
      }
    },
    handleSelectionChange(selectedRows) {
      console.log('选中的行数据:', selectedRows)
      this.selectedRows = selectedRows
      // 这里的 selectedRows 是一个数组，包含所有选中的行对象
    },
    getCurrentTimestamp() {
      const now = new Date();
      const pad = (n) => n.toString().padStart(2, '0');
      const year = now.getFullYear();
      const month = pad(now.getMonth() + 1);
      const day = pad(now.getDate());
      const hour = pad(now.getHours());
      const minute = pad(now.getMinutes());
      const second = pad(now.getSeconds());
      return `${year}${month}${day}${hour}${minute}${second}`;
    },
    temEvent() {
      console.log(this.selectedRows);
      console.log(this.page);
      let id = []
      if (this.selectedRows) {
        id = this.selectedRows.map(i => i.id)
      }
      console.log(id);
      let params = {
        orgCode: this.searchItem.org_code,
        ids: id,
        areaId: this.searchItem.area_code,
        roomCode: this.searchItem.room_code,
        roomType: this.searchItem.room_type
      }
      this.$store.dispatch('authPostRequestBlob', {
        url: this.$path.bsp_pam_infoManage_exportExcel,
        params
      }).then(res => {
        let timestamp = this.getCurrentTimestamp();
        let fileName = `监室信息数据-${timestamp}.xls`;

        let blobURL = URL.createObjectURL(res);
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = blobURL;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobURL);
        this.$nextTick(() => {
          // this.on_show_table();
          this.reset()
          // this.$set(this.selectedRows,[])
        })
      })
    },

  }
}
</script>

<style lang="less" scoped>
.fm-content-card-title {
  box-sizing: border-box;
  margin: 12px 32px 11px 29px;
  border-radius: 12px;
  padding: 20px; /* 保持不变 */
  width: 98%;
  height: 65px;
  box-shadow: 1px 1px 5px 2px rgba(0, 0, 0, 0.1);

  span {
    font-size: 20px;
  }
}

.ivu-btn:nth-of-type(n+1) {
  margin-right: 10px;
}

.dbxx-wrap-search {
  background: rgba(247, 249, 252, .9);
  padding: 10px 15px;
  /* margin-bottom: 10px; */
  // overflow: hidden;
  border: 1px solid #cee0f0;
  margin: 16px 10px;
}

.pageWrap {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 6px 0;
  margin-top: 6px;
  // border-top: 1px solid #f1f9fa;
  width: 100%;
}

.first-card {
  border-radius: 4px 0 0 4px !important;
  margin-left: 12px !important;
}

.card-method-change:last-of-type {
  border-radius: 0 4px 4px 0;
  border-left: none;
}

.card-method-change.is-active {
  border: 1px solid #317FF5;
  background: #F0F4FF;
}

.card-method-change {
  width: 40px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #cee0f0;
  padding: 5px 12px;

  &:hover {
    border: 1px solid #317FF5;
  }
}

.action-btn {
  width: 100%;
  height: 50px;
  // line-height: 90px;
  display: flex;
  justify-content: space-between;
  padding: 0px 10px;
}



.fm-content-card {
  padding: 0px 20px;
  // padding-top: 30px;
  // padding-left: 30px;
  // background: red;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  // height:503px;
  width: 100%;
  overflow: auto;
}

.fm-content-card-item {
  width: 16%;
  min-height: 140px;
  min-height: 150px;
  // height: 8.75rem;
  background: #fff;
  padding: 10px;
  margin-right: 20px;
  margin-bottom: 30px;
  border-radius: 6px;
  border: 1px solid transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), /* 顶部阴影 - 轻微 */ 2px 0 4px rgba(0, 0, 0, 0.1), /* 左侧阴影 - 轻微 */ -2px 0 4px rgba(0, 0, 0, 0.1), /* 右侧阴影 - 轻微 */ 0 6px 12px rgba(0, 0, 0, 0.2); /* 底部阴影 - 更明显 */
}

.fm-content-card-item:hover {
  border: 1px solid #91baf8;
  cursor: pointer;
}

.fm-content-card-item:nth-child(5n) {
  // background: pink;
  // margin-right: 200px;
}

.card-item-top {
  border-bottom: 1px solid #e6e5e5bd;
  display: flex;
  padding-bottom: 10px;

}

.tabs {
  display: inline-block;
  padding: 0px 4px;
  color: rgb(63, 158, 255);
  background: rgb(233, 244, 255);
  font-size: 14px;
  border-radius: 4px;
}

.tabs-room-type {
  display: inline-block;
  padding: 0px 4px;
  font-size: 14px;
  border-radius: 4px;
  margin-left: 5px
}

.card-item-top-info {
  display: flex;
  align-items: center;
  line-height: 28px;
}

// .card-item-bottom{
//   position: relative;
//   p{
//     font-size: 14px;
//     line-height: 30px;
//     color: rgb(167,176,186);
//   }
// }
// .activeContent{
//   position: relative;
//   top: 10;
//   left: 0;
//   width: 60px;
//   height: 60px;
//   background: rgb(167,176,186);
//   border-radius: 6px;
//   z-index: 10;
//   margin: 0;
//   padding: 0;
// }
.card-item-bottom {
  position: relative;
  padding-top: 10px;
  // padding-bottom: 24px; /* 为下拉菜单预留空间 */

  p {
    font-size: 14px;
    line-height: 22px;
    color: rgb(167, 176, 186);
  }
}

.action-menu-container {
  position: absolute;
  bottom: 0;
  right: 0;
}

.action-menu-trigger {
  cursor: pointer;
  width: 20px; /* 适当大小 */
  height: 20px;
  padding: 4px;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.1);
  }
}

.action-menu {
  position: absolute;
  top: 100%; /* 从触发器向上弹出 */
  right: 0;
  width: 80px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  margin-bottom: 4px; /* 与触发器的间距 */

  .menu-item {
    padding: 8px 12px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    text-align: center;
    display: flex;
    align-items: center;

    &:hover {
      background-color: #f5f5f5;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 205px;
}

.roomSex {
  width: 50px;
  height: 50px;
  margin-right: 8px;
}

.hover-underline:hover {
  text-decoration: underline;
}

.bsp-base-form {
  // min-height: 800px;
  // height: 100%;
}

.base-form {
  // width: 100%;
  // height: 100%;
  background: #fff;
  padding: 10px;
  margin: 10px;
}

.bsp-base-content {
  top: 15px !important;
}
</style>
