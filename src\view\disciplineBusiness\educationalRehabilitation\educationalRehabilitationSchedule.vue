<template>
    <div style="width: 100%;height: 100%;">
        <div class="table-container" v-if="tableContainer">
            <s-DataGrid ref="grid" funcMark="kfjykb" :customFunc="true">
                <!-- 设备报修登记 -->
                <template slot="customHeadFunc" slot-scope="{ func }">
                    <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':kfjykb:add')"
                        @click.native="handleAddKb('add')">创建课表</Button>
                </template>
                <template slot="customRowFunc" slot-scope="{ func, row, index }">
                    <Button type="primary" style="margin-left: 10px;"
                        v-if="func.includes(globalAppCode + ':kfjykb:sp') && row.status == '01'"
                        @click.native="handleSpKb(index, row)">审批</Button>
                    <Button style="margin-left: 10px;"
                        v-if="func.includes(globalAppCode + ':kfjykb:xq') && row.status != '01'"
                        @click.native="handleXqKb(index, row)">详情</Button>
                </template>
            </s-DataGrid>
        </div>
        <div v-if="handleAddKbContainer" class="handle-add-kb-container">
            <div class="header-container">
                <div class="left-kb-name">
                    <span>课表名称</span>
                </div>
                <div class="kb-time-through">
                    <Button>
                        <Icon type="ios-arrow-back" />
                        上一周
                    </Button>
                    <DatePicker type="daterange" v-model="dateThrough" placement="bottom-end" placeholder="选择日期"
                        style="width: 200px;margin: 0 10px;" />
                    <Button>
                        下一周
                        <Icon type="ios-arrow-forward" />
                    </Button>
                </div>
                <div class="setting-kb-time-through">

                    <Button @click="handleAddTimeSet">
                        课表时段管理
                        <Icon type="md-settings" />
                    </Button>
                </div>
                <Modal v-model="addTimeSet" :mask-closable="false" :closable="false" class-name="select-use-modal"
                    width="900" title="新增课程">
                    <Form ref="form" :model="formDynamic" :label-width="120">
                        <div class="cover-time-set">
                            <template v-for="(item, index) in formDynamic.items">
                                <FormItem v-if="item.status" :key="index" label="课程时段"
                                    :prop="'items.' + index + '.time'"
                                    :rules="{ required: true, type: 'array', message: '课程时段不能为空', trigger: 'blur' }">
                                    <Row>
                                        <Col span="18">
                                        <TimePicker v-model="item.time" format="HH:mm" type="timerange"
                                            placement="bottom-end" style="width: 560px" />
                                        <!-- <DatePicker type="daterange" v-model="item.time"  placement="bottom-end" style="width: 560px" /> -->
                                        </Col>
                                        <Col span="4" offset="1" v-if="index > 0">
                                        <Icon type="ios-trash-outline" @click="handleRemove(index)" />
                                        </Col>
                                    </Row>
                                </FormItem>
                            </template>
                        </div>

                        <FormItem>
                            <Row>
                                <Col span="18">
                                <Button type="dashed" long @click="handleAdd" icon="md-add">新增</Button>
                                </Col>
                            </Row>
                        </FormItem>
                    </Form>
                    <div slot="footer">
                        <Button type="primary" @click="handleSumbit('form')" class="save">确 定</Button>
                        <Button @click="handleCancel('form')" class="save">关 闭</Button>
                    </div>
                </Modal>
            </div>

            <div class="content-container">
                <div class="left-add-kb-type">
                    <div class="kb-type">课程类型</div>
                    <div class="search-kb-type">
                        <Input placeholder="评估课程" style="width: auto">
                        <template #suffix>
                            <Icon type="ios-search" />
                        </template>
                        </Input>
                    </div>
                    <div class="draggle-container-kb-type">
                        <div class="add-input">
                            <Input v-model="form.coursesName" placeholder="输入新课程名称，添加课程" />
                            <Button @click="isOpen = true">
                                <Icon type="md-add" />
                                添加课程
                            </Button>
                        </div>
                        <div class="draggle-container">
                            <draggable id="left-draggable" :list="isAddKbList"
                                :group="{ name: 'eduList', pull: 'clone', put: false }" :move="checkMove"
                                drag-class="dragging-item" ghost-class="ghost-item" :touch-start-threshold="5"
                                @choose="onChoose" animation="200" @start="onDragStart" @end="onDragEnd"
                                @dragstart="dragstart">
                                <div v-for="(item, index) in isAddKbList" :key="index" class="kb-list-cover-container">
                                    <div :style="{ background: item.coursesColor }">{{ item.coursesName }}</div>
                                </div>
                            </draggable>
                            <addCourseModal :isShowhandleModal="isOpen" :form="form" @updateTable="updateTable" />
                        </div>

                    </div>
                </div>
                <div class="right-table">

                    <table>
                        <!-- 表头：日期行 -->
                        <thead>
                            <tr>
                                <th style="height: 46px;line-height: 46px;">时段 日期</th>
                                <th colspan="3" v-for="(dateItem, index) in newDatesWithDay" :key="index"
                                    style="text-align: center;">
                                    <p v-if="dateItem.day" style="font-size: larger;font-weight: 700;color: #555;">
                                        {{ dateItem.day }}
                                    </p>
                                    <p v-if="dateItem.date" style="font-size: smaller;font-weight: 300;color: #7f7f7f;">
                                        {{ dayjs(dateItem.date).format('MM-DD') }}
                                    </p>
                                </th>
                            </tr>
                        </thead>
                        <!-- 数据行：时间段作为首列 -->
                        <tbody>
                            <template v-if="newAreaList1.length > 0">
                                <tr>
                                    <td style="background: #f59a23;color: #fff;">
                                        <p>戒区</p>
                                    </td>
                                    <td style="height: 80px;" v-for="(item, idx) in newAreaList1" :key="idx">
                                        <p style="width: 120px;">{{ item.areaName }}</p>
                                        <Button size="small" v-if="idx > 2" :disabled="copyDayFlag(idx)"
                                            @click="copyDay(idx)">
                                            <Icon type="md-copy" />
                                            复制昨日
                                        </Button>
                                    </td>
                                </tr>
                            </template>
                            <!--    -->
                            <template v-if="areaWithTimeSoltList1.length > 0">
                                <tr v-for="(rowItem, rowIndex) in areaWithTimeSoltList1" :key="rowItem.id">
                                    <td style="background-color: #7991c8;color: #fff;">{{ rowItem.timeSlotCode }}</td>
                                    <td v-for="(item, index) in rowItem.areaList" class="dish-cell" :class="{
                                        // 'drop-zone-active': isDragOver && !isDragOverInvalid,
                                        'drop-zone-invalid': isDragOverInvalid,
                                        // 'has-dishes': item.cooks && item.cooks.length > 0
                                    }" :key="`${rowIndex}-${index}-${item.id}`">
                                        <draggable :list="item.coursesNameList" :options="options"
                                            :key="`${rowIndex}-${index}-${rowIndex}`" animation="200"
                                            ghost-class="ghost-item" @start="onDragStartTab" @end="onDragEndTab"
                                            @change="onChange" @dragstart="dragstart"
                                            @dragover.native.prevent="onDragOver" @dragleave.prevent.stop="onDragLeave"
                                            @drop.native.prevent.stop="onDrop" :move="checkMove2"
                                            @add="(evt) => onDishAdded(evt, rowIndex, index)" class="drop-zone-full"
                                            :data-cell-id="`cell-${rowIndex}-${index}`"
                                            v-if="item && item.coursesNameList !== undefined">
                                            <div class="dish-item" v-for="(info, idx) in (item.coursesNameList || [])"
                                                :key="`${rowIndex}-${index}-${item.id}-${idx}`">
                                                <div class="dish-item-content">
                                                    <div class="dish-item-name"
                                                        :style="{ background: info.coursesColor, width: '60px', margin: '5px', padding: '5px', fontSize: '12px', borderRadius: '2px' }"
                                                        :title="info.coursesName">{{ info.coursesName }}
                                                        <!-- <Tooltip content="移除课程" placement="bottom">
                                                            <Icon type="md-close" class="remove-dish-icon"
                                                                @click="delSub(rowIndex, index, idx)" />
                                                        </Tooltip> -->
                                                    </div>

                                                </div>
                                            </div>
                                        </draggable>

                                    </td>

                                </tr>

                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="handleReset" style="margin-right: 10px;">取消</Button>
                <Button type="primary" @click="handleSubmit">确认</Button>
            </div>
        </div>
        <div v-if="detailContainer" class="handle-detail-kb-container">
            <header class="header-container">
                <div>
                    <h2>{{ detailInfo.planName }}</h2>
                    <span>{{ detailInfo.statusName }}</span>
                </div>
                <ul>
                    <li>
                        <span>课表时间：</span>
                        <span>{{ kbsj }}</span>
                    </li>
                    <li>
                        <span>创建人：</span>
                        <span>{{ detailInfo.addUserName }}</span>
                    </li>
                    <li>
                        <span>创建时间：</span>
                        <span>{{ detailInfo.addTime }}</span>
                    </li>
                </ul>
            </header>
            <div class="detail-table">
                <table>
                    <!-- 表头：日期行 -->
                    <thead>
                        <tr>
                            <th style="height: 46px;line-height: 46px;">时段 日期</th>
                            <th colspan="3" v-for="(dateItem, index) in newDatesWithDay" :key="index"
                                style="text-align: center;">
                                <p v-if="dateItem.day" style="font-size: larger;font-weight: 700;color: #555;">
                                    {{ dateItem.day }}
                                </p>
                                <p v-if="dateItem.date" style="font-size: smaller;font-weight: 300;color: #7f7f7f;">
                                    {{ dayjs(dateItem.date).format('MM-DD') }}
                                </p>
                            </th>
                        </tr>
                    </thead>
                    <!-- 数据行：时间段作为首列 -->
                    <tbody>
                        <template v-if="newAreaList.length > 0">
                            <tr>
                                <td style="background: #f59a23;color: #fff;">
                                    <p>戒区</p>
                                </td>
                                <td style="height: 80px;" v-for="(item, idx) in newAreaList" :key="idx">
                                    <p style="width: 120px;">{{ item.areaName }}</p>
                                </td>
                            </tr>
                        </template>
                        <!--    -->
                        <template v-if="newEdurehabList.length > 0">
                            <tr v-for="(rowItem, rowIndex) in newEdurehabList" :key="rowIndex">
                                <td style="background-color: #7991c8;color: #fff;">{{ rowItem.timeSlotCode }}</td>
                                <td v-for="(item, index) in rowItem.areaList" :key="index">
                                    <div class="dish-item" v-for="(info, idx) in (item.coursesNameList || [])"
                                        :key="idx">
                                        <div class="dish-item-content">
                                            <div class="dish-item-name"
                                                :style="{ background: info.coursesColor, width: '60px', margin: '5px', padding: '5px', fontSize: '12px', borderRadius: '2px' }"
                                                :title="info.coursesName">{{ info.coursesName }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="handleBack" style="margin-right: 10px;">返回</Button>
            </div>
        </div>
        <Modal v-model="spModal" :mask-closable="false" :closable="false" class-name="select-use-modal" width="900"
            title="课程审批">
            <Form ref="formSp" :model="formSp" :label-width="120">
                <FormItem label="课表时间：" prop="coursesName">
                    {{ kbsj }}
                </FormItem>
                <FormItem label="审批结果：" prop="status"
                    :rules="{ required: true, message: 'Please select gender', trigger: 'change' }">
                    <RadioGroup v-model="formSp.status">
                        <Radio label="04">通过</Radio>
                        <Radio label="02">不通过</Radio>
                    </RadioGroup>
                </FormItem>
                <FormItem label="审批意见：" prop="approvelComment"
                    :rules="{ required: true, message: '请输入课程名称', trigger: 'blur' }">
                    <Input v-model="formSp.approvelComment" placeholder="请输入审批意见" type="textarea"
                        :autosize="{ minRows: 2, maxRows: 5 }">
                    </Input>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button type="primary" @click="handleSpSumbit('formSp')" class="save">确 定</Button>
                <Button @click="handleSpCancel('formSp')" class="save">关 闭</Button>
            </div>
        </Modal>

    </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions, mapState } from 'vuex'
import draggable from "vuedraggable";
import addCourseModal from './addCourseModal.vue';
import { number } from 'echarts';
export default {
    name: "educationalRehabilitationSchedule",
    data() {

        return {
            options: {
                group: {
                    name: 'eduList',//组名为itxst
                    pull: false,//'clone',
                    put: true,
                },
                setData: this.setData()
            },
            tableContainer: true,
            handleAddKbContainer: false,
            detailContainer: false,
            handleModal: false,
            spModal: false,
            addkcName: "",
            dateThrough: [],
            isAddKbList: [],
            isOpen: false,
            form: {
                coursesColor: "",
                coursesName: "",
                isEnabled: 1
            },
            formSp: {
                approvelComment: "",
                status: "",
            },
            kbsj: "",
            addTimeSet: false,
            index: 1,
            formDynamic: {
                items: [
                    {
                        time: [],
                        index: 1,
                        status: 1
                    }
                ]
            },
            datesWithDay: [],
            timeSlotData: [],
            areaList: [],
            newAreaList: [],
            newAreaList1: [],
            newDatesWithDay: [],
            areaWithTimeSoltList: [],
            areaWithTimeSoltList1: [],
            drupAreaWithTimeSoltList: [],
            detailInfo: {},
            rowId: "",
            newEdurehabList: [],
            isDragOverInvalid: false
        }

    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        setData(data) {
            console.log(data, '1212')
        },
        handleAddKb() {
            this.handleAddKbContainer = true
            this.tableContainer = false
        },
        handleReset() {
            this.handleAddKbContainer = false
            this.tableContainer = true
        },
        updateTable() {
            this.isOpen = false
            this.handleGetedurehabCoursesList()
        },
        handleSpKb(idx, row) {
            this.rowId = row.id
            this.kbsj = row.kbsj
            this.spModal = true
        },
        handleSubmit() {

        },
        onDragStartTab() {
            console.log(this.areaWithTimeSoltList, 'this.areaWithTimeSoltListonDragStartTab')

        },
        onChoose(e, value) {
            console.log(e, value, 'this.e,value')
        },
        onChange(e, value) {
            console.log(e, value, 'this.e,value')
        },
        onDragEndTab() {
            console.log(this.areaWithTimeSoltList, 'this.areaWithTimeSoltListonDragStartTab2')

        },
        onDragStart() {
            //   console.log(this.areaWithTimeSoltList,'this.areaWithTimeSoltList')

        },
        dragstart(data, value) {
            console.log(data, value, 'dragstart')
        },
        onDragEnd() {
        },
        copyDayFlag() { },
        copyDay() { },
        checkMove(evt) {
            if (evt.to.id === "left-draggable") {
                return false;
            }
            // 检查是否重复菜品 - 在拖拽过程中进行检查
            if (evt.draggedContext && evt.relatedContext) {
                const draggedItem = evt.draggedContext.element;
                const targetList = evt.relatedContext.list;

                if (draggedItem && targetList) {

                    // 检查目标列表中是否已存在相同菜品
                    const isDuplicate = targetList.some(item =>
                        (item.coursesCode && draggedItem.coursesCode && item.coursesCode === draggedItem.coursesCode) ||
                        (item.coursesColor && draggedItem.coursesColor && item.coursesColor === draggedItem.coursesColor) ||
                        (item.id && draggedItem.id && item.id === draggedItem.id)
                    );

                    if (isDuplicate) {
                        // 设置无效拖拽状态，用于显示红色边框
                        this.isDragOverInvalid = true;
                        return false;
                    } else {
                        // 重置无效状态
                        this.isDragOverInvalid = false;
                    }
                }
            }

            return true;
        },
        checkMove2(evt) {
            console.error(evt.draggedContext, evt.relatedContext);
            // console.error(this.areaWithTimeSoltList, '2checkMove[this.areaWithTimeSoltList]');
            // 防止拖拽回左侧菜品列表

        },
        onDishAdded(evt, rowIndex, cellIndex) {

            // 获取拖入的数据（深拷贝避免引用污染）
            // console.error(rowIndex, '第几行', evt);
            // console.error(cellIndex, '第几列');
            // console.error(this.areaWithTimeSoltList[rowIndex].areaList[cellIndex]);

            const newItem = JSON.parse(JSON.stringify(evt.item._underlying_vm_));
            // console.error(newItem, '[newItem===>>>>>>newItem]');

            // // 定位到当前单元格数据
            // console.error(this.areaWithTimeSoltList[rowIndex].areaList[cellIndex]);
            // console.error(this.areaWithTimeSoltList, '[this.areaWithTimeSoltList]');

            // this.$set(
            //     this.areaWithTimeSoltList[rowIndex].areaList[cellIndex],
            //     'coursesNameList',
            //     [newItem]
            // );
            // 阻止默认整列更新
            // evt.preventDefault();
            // evt.stopPropagation();
        },
        onDragOver(evt) {
            // this.drupAreaWithTimeSoltList=JSON.parse(JSON.stringify(this.areaWithTimeSoltList))
            // console.log(this.areaWithTimeSoltList, 'this.areaWithTimeSoltList')
            evt.preventDefault();
            evt.stopPropagation();
        },
        onDragLeave(evt) {
            // console.log(this.areaWithTimeSoltList, 'this.areaWithTimeSoltListonDragLeave')
            evt.preventDefault();
            evt.stopPropagation();
        },
        onDrop(evt) {
            // console.log(this.areaWithTimeSoltList, 'this.areaWithTimeSoltListonDrop')
            evt.preventDefault();
            evt.stopPropagation();
        },
        handleGetedurehabCoursesList() {
            let params = {
                "coursesCode": "",
                "coursesColor": "",
                "coursesName": "",
                "isEnabled": 1,
                "orgCode": this.$store.state.common.orgCode
            }
            this.authPostRequest({ url: this.$path.edurehabCourses_list, params }).then(res => {
                if (res.success) {
                    this.isAddKbList = res.data
                }
            })
        },
        handleAddTimeSet() {
            this.addTimeSet = true
            if (this.timeSlotData && this.timeSlotData.length > 0) {
                this.formDynamic.items = []
                this.timeSlotData.forEach((ele, index) => {
                    ele.status = 1
                    ele.index = Number(index + 1)
                    ele.time = [ele.startTime, ele.endTime]
                    this.formDynamic.items.unshift(ele)
                })
            }
        },
        handleAdd() {
            this.index++;
            this.formDynamic.items.push({
                time: [],
                index: this.index,
                status: 1,
            });
        },
        handleRemove(index) {
            this.formDynamic.items[index].status = 0;
        },
        handleSumbit(name) {
            this.$refs[name].validate((valid) => {
                if (valid) {
                    this.timeList = this.formDynamic.items.map(item => {
                        return {
                            startTime: item.time[0],
                            endTime: item.time[1],
                        }
                    })
                    this.authPostRequest({ url: this.$path.edurehabTimeSlot_batchCreate, params: this.timeList }).then(res => {
                        if (res.success) {
                            this.addTimeSet = false
                            this.handleGetPlanAreaList()
                            this.$refs[name].resetFields();
                            this.handleGetTimeList()
                            this.$Message.success('添加成功')

                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                } else {
                    this.$Message.error('验证未通过');
                }
            })
        },
        handleCancel(name) {
            this.addTimeSet = false
            this.$refs[name].resetFields();
        },
        handleSpCancel(name) {
            this.$refs[name].resetFields();
            this.spModal = false
        },
        handleSpSumbit(name) {
            this.$refs[name].validate((valid) => {
                if (valid) {
                    this.authPostRequest({ url: this.$path.edurehabCoursesPlan_approval, params: { approvelComment: this.formSp.approvelComment, status: this.formSp.status, id: this.rowId } }).then(res => {
                        if (res.success) {
                            this.spModal = false
                            this.rowId = ""
                            this.$refs[name].resetFields();
                            this.$Message.success('添加成功')
                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                } else {
                    this.$Message.error('验证未通过');
                }
            })
        },
        handleGetTimeList() {
            this.authPostRequest({ url: this.$path.edurehabTimeSlot_list, params: {} }).then(res => {
                if (res.success) {
                    this.timeSlotData = res.data
                }
            })
        },
        handleGetPlanTime() {
            this.authGetRequest({ url: this.$path.edurehabCoursesPlan_getPlanTime, params: { orgCode: this.$store.state.common.orgCode } }).then(res => {
                if (res.success) {
                    const { startDateStr, endDateStr } = res.data
                    this.dateThrough[0] = startDateStr
                    this.dateThrough[1] = endDateStr
                    this.datesWithDay = this.getDatesWithDays(startDateStr, endDateStr)
                }
            })
        },
        getDatesWithDays(startDate, endDate) {
            // 1. 初始化日期对象并验证
            const start = this.dayjs(startDate).startOf('day');
            const end = this.dayjs(endDate).endOf('day');

            if (start.isAfter(end)) {
                throw new Error('开始日期不能晚于结束日期');
            }

            // 2. 星期映射表（中文）
            const weekdays = [
                '星期日', '星期一', '星期二',
                '星期三', '星期四', '星期五', '星期六'
            ];

            // 3. 遍历日期范围
            const result = [];
            let current = start;

            while (current.isBefore(end) || current.isSame(end, 'day')) {
                result.push({
                    date: current.format('YYYY-MM-DD'),
                    day: weekdays[current.day()]  // 0-6转中文星期
                });
                current = current.add(1, 'day');
            }

            return result;
        },
        handleGetPlanAreaList() {
            this.authGetRequest({ url: this.$path.edurehabCoursesPlan_getPlanArea, params: { orgCode: this.$store.state.common.orgCode } }).then(res => {
                if (res.success) {
                    this.areaList = res.data;
                    this.newAreaList = Array.from({ length: this.datesWithDay.length }, () => [...this.areaList]).flat();
                    this.newAreaList1 = JSON.parse(JSON.stringify(this.newAreaList))

                    this.areaWithTimeSoltList = this.combineScheduleData(this.timeSlotData, this.newAreaList)
                    this.areaWithTimeSoltList1 = JSON.parse(JSON.stringify(this.areaWithTimeSoltList))
                    console.error(this.areaWithTimeSoltList, '[areaWithTimeSoltList]');
                }
            })
        },
        combineScheduleData(test1, test2) {
            console.log(test1, test2, 'test1, test2')
            // 创建重复7次的基础区域列表（每组包含test2的所有区域）
            const baseAreaList = Array(1).fill().flatMap(() =>
                test2.map(area => ({
                    areaId: area.areaId,
                    areaName: area.areaName,
                    timeSlotList: null,
                    coursesNameList: [] // 确保每个区域都有空数组
                }))
            );

            // 组合最终数据结构
            return test1.map(timeSlot => ({
                id: timeSlot.id++,
                timeSlotId: timeSlot.id,
                timeSlotCode: timeSlot.timeSlotCode,
                startTime: timeSlot.startTime,
                endTime: timeSlot.endTime,
                areaList: [...baseAreaList] // 为每个时间段创建独立副本
            }));
        },
        delSub(menuIndex, index, idx) {
            console.error(menuIndex, index, idx);

            // this.areaWithTimeSoltList[menuIndex].coursesNameList[index].coursesNameList.splice(idx, 1);
            // this.$Message.success('课程已移除');
        },
        transformToArrList(edurehabList) {
            // 1. 创建时间段索引映射表
            const timeSlotMap = new Map();

            // 2. 遍历原始数据结构
            edurehabList.forEach(dateGroup => {
                dateGroup.jqAreaVOList.forEach(area => {
                    area.timeSlotList.forEach(timeSlot => {
                        const key = `${timeSlot.timeSlotCode}-${timeSlot.startTime}-${timeSlot.endTime}`;

                        // 3. 初始化时间段结构
                        if (!timeSlotMap.has(key)) {
                            timeSlotMap.set(key, {
                                id: this.generateUniqueId(), // 生成唯一ID的方法
                                timeSlotCode: timeSlot.timeSlotCode,
                                startTime: timeSlot.startTime,
                                endTime: timeSlot.endTime,
                                areaList: []
                            });
                        }

                        // 4. 处理课程数据
                        const coursesNameList = timeSlot.listRecord
                            // .filter(record => record.coursesCode) // 过滤有效课程
                            .map(record => ({
                                id: record.id,
                                coursesCode: record.coursesCode,
                                coursesName: record.coursesName,
                                coursesColor: record.coursesColor
                            }));

                        // 5. 构建区域数据
                        const areaObj = {
                            areaId: area.areaId,
                            areaName: area.areaName,
                            timeSlotList: null, // 按目标结构设为null
                            coursesNameList
                        };

                        // 6. 添加到时间段
                        timeSlotMap.get(key).areaList.push(areaObj);
                    });
                });
            });
            // 7. 返回最终数组
            return Array.from(timeSlotMap.values());
        },
        generateUniqueId() {
            return `id_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
        },
        handleXqKb(idx, row) {
            this.kbsj = row.kbsj
            this.authGetRequest({ url: this.$path.edurehabCoursesPlan_get, params: { id: row.id } }).then(res => {
                if (res.success) {
                    this.detailInfo = res.data
                    this.newEdurehabList = this.transformToArrList(res.data.edurehabList)
                    console.error(this.newEdurehabList, '[------------------------------------------------------]');

                    this.tableContainer = false
                    this.detailContainer = true
                }
            })
        },
        handleBack() {
            this.detailContainer = false
            this.tableContainer = true
        },
    },

    components: {
        sDataGrid,
        draggable,
        addCourseModal
    },

    created() {
        this.handleGetedurehabCoursesList()
        this.handleGetTimeList()
        this.handleGetPlanTime()

        setTimeout(() => {
            this.handleGetPlanAreaList()
        }, 1000)
    },

    computed: {},

    watch: {
        datesWithDay: {
            handler(newVal) {
                // this.newAreaList = Array.from({ length: newVal.length }, () => [...this.areaList]).flat();
                this.newDatesWithDay = newVal.reduce((acc, curr) => {
                    acc.push(curr); // 添加当前日期对象
                    acc.push({});   // 添加空对象
                    return acc;
                }, [])
            },
        },
        areaWithTimeSoltList: {
            handler(newVal) {
                // console.error(newVal, '======================>>>>>>>>>>>>>');

            },
            immediate: true,
            deep: true
        },
        'formSp.status': {
            handler(newVal) {
                if (newVal == '02') {
                    this.formSp.approvelComment = "不同意"
                } else {
                    this.formSp.approvelComment = "同意"
                }
            }
        }
    }

}

</script>

<style scoped lang="less">
.handle-add-kb-container {
    width: 100%;
    height: 100%;
    background: #e8eaec;

    .header-container {
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 10px 20px;
        width: 100%;
        background: #fff;
    }

    .content-container {
        margin-top: 10px;
        width: 100%;
        height: calc(~'100% - 80px');
        background-color: #fff;
        display: flex;

        .left-add-kb-type {
            width: 358px;

            .kb-type {
                height: 30px;
                width: 100%;
                text-align: center;
                height: 44px;
                line-height: 44px;
                background-color: rgba(43, 95, 218, 1);
                font-weight: 700;
                font-style: normal;
                font-size: 14px;
                color: #FFFFFF;
            }


            .search-kb-type {
                height: 57px;
                line-height: 57px;
                text-align: center;
                background-color: rgba(242, 246, 252, 1);
            }

            .draggle-container-kb-type {
                overflow-y: auto;
                height: calc(~'100% - 150px');

                .add-input {
                    margin-top: 5px;
                    display: flex;
                    justify-content: space-between;
                }

                #left-draggable {
                    max-height: 320px;
                    display: flex;
                    flex-flow: wrap;

                    .kb-list-cover-container {
                        margin: 10px 10px;

                        div {
                            width: 150px;
                            height: 55px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            box-sizing: border-box;
                            border-width: 1px;
                            border-style: solid;
                            border-color: rgba(215, 215, 215, 1);
                            border-radius: 4px;
                        }
                    }
                }

            }

        }


    }
}

.detail-table {
    max-width: 1260px;
    max-height: 400px;
    overflow: auto;

    table {
        border-collapse: collapse;
        margin-left: 10px;
        margin-top: 10px;
        // table-layout: auto;
    }

    thead tr {
        background-color: #fff;
    }

    thead tr th:nth-child(2n) {
        border-right: solid 1px #ddd;
    }

    thead tr th:nth-child(2n+1) {
        display: none;
    }

    thead tr th:nth-child(1) {
        display: block;
        border-left: solid 1px #ddd;
    }

    th,
    td {
        border: 1px solid #ddd;
        text-align: center;
        width: 128px;
    }

    th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    /* 表头背景 */
    td:first-child {
        background-color: #fff;
    }
}

.right-table {
    flex: 1;
    // max-width: 900px;
    width: calc(~'100% -400px');
    overflow: auto;

    table {
        border-collapse: collapse;
        margin-left: 10px;
        margin-top: 10px;
        // table-layout: auto;
    }

    thead tr {
        background-color: #fff;
    }

    thead tr th:nth-child(2n) {
        border-right: solid 1px #ddd;
    }

    thead tr th:nth-child(2n+1) {
        display: none;
    }

    thead tr th:nth-child(1) {
        display: block;
        border-left: solid 1px #ddd;
    }

    th,
    td {
        border: 1px solid #ddd;
        text-align: center;
        width: 128px;
    }

    th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    /* 表头背景 */
    td:first-child {
        background-color: #fff;
    }
}

.handle-detail-kb-container {
    width: 100%;
    height: 100%;

    header {
        background: #f8f8f9;
        border-radius: 4px;
        margin: 0 10px 10px 10px;
        padding: 10px;

        div {
            display: flex;
            align-items: center;
        }

        ul {
            list-style: none;
            display: flex;
            align-items: center;

            li {
                margin-right: 20px;
            }
        }
    }
}

.dish-item-content {
    position: relative;

    .dish-item-name {
        position: relative;

        .remove-dish-icon {
            position: absolute;
            right: -13px;
            top: -18px;
        }
    }

}

.dish-cell {
    &.drop-zone-invalid {
        // background: #fff2f0;
        border-color: #ed4014;

        .drop-zone-full {
            border-color: #ed4014;
            background: rgba(237, 64, 20, 0.05);
            cursor: not-allowed;
        }
    }
}

/deep/.ivu-btn-default {
    height: 32px;
}
</style>
