<template>
  <div>
	<div class="bsp-base-form">
		<div class="bas-base-content">
			<div class="keyperson-content">
				<div class="keyperson-content-left">
					<div class="selectPerson">
						<personnel-selector
							v-model="formData.jgrybm"
							mode="edit"
							title="被监管人员"
							placeholder="点击选择被监管人员"
							personnel-type="ZS"
							:show-case-info="true"
							:enable-scan="true"
							:show-scan-tip="true"
							@change="handlePersonnelChange"
						/>
					</div>

					<div class="personList">
						<Tabs value="name1">
							<TabPane label="单独关押记录" name="name1">
								<alonelmprisonRecords :takeNotes="takeNotes" :stepList="stepList" :current="current" :stepType="stepType"></alonelmprisonRecords>
							</TabPane>
						</Tabs>
					</div>
				</div>
				<div class="keyperson-content-right">
					<div class="keyperson-content-rightTop">
						<p class="sys-sub-title">单独关押登记</p>
						<div>
							<Form ref="formData" :model="formData" :label-width="150" :label-colon="true" class="base-form-container">
								<Row>
									<Col span="16">
										<FormItem label="单独关押原因" prop="registerReason" :rules="[{ trigger: 'blur', message: '请选择单独关押原因', required: true }]" style="width: 100%;">
											<s-dicgrid v-model="formData.registerReason" style="width: 100%;" dicName="ZD_DDGY_DJYY" />
										</FormItem>
									</Col>
								</Row>
								<Row>
									<Col span="24">
										<FormItem label="具体原因" prop="specificReason" :rules="formData.registerReason === '04' ? [{ trigger: 'blur', message: '请填写具体原因', required: true }] : [{}]" style="width: 100%;">
											<Input v-model="formData.specificReason"  maxlength="125"
													type="textarea" show-word-limit :autosize="{ minRows: 4, maxRows: 5 }"
													placeholder="请输入" />
										</FormItem>
									</Col>
								</Row>
								<Row>
									<Col span="8">
										<FormItem label="原监室号" style="width: 100%;">
											<p style="line-height: 35px;">{{ formData.oldRoomName }}</p>
										</FormItem>
									</Col>
									<Col span="8">
										<FormItem label="新监室号" prop="newRoomId" :rules="[{ trigger: 'blur', message: '请选择新监室号', required: true }]" style="width: 100%;">
											<s-dicgrid v-model="formData.newRoomId" v-if="roomShow" requestType="loopData" :loopData="roomList" showField="roomName" keyField="roomCode" @values="roomChange" style="width: 100%;" />
										</FormItem>
									</Col>
								</Row>
								<Row>
									<Col span="24">
										<FormItem label="是否关联惩罚" prop="isAssociatedPunishment" :rules="[{ trigger: 'blur', message: '请选择', required: true }]" style="width: 100%;">
											<i-Switch v-model="isPunishmentSync" style=" margin-top: 8px;" />
										</FormItem>
									</Col>
								</Row>
								<Row v-if="isPunishmentSync">
									<Col span="24">
										<FormItem label="惩  罚  措  施" prop="punishmentMeasures" :rules="punishmentRules" :required="true" style="width: 100%;">
											<CheckboxGroup v-model="safePunishmentMeasures">
												<div v-for="(row, index) in chunkedFruitList" :key="index" class="checkbox-row">
													<Checkbox
														v-for="item in row"
														:key="item.code"
														:label="item.code"
														class="checkbox-item"
													>
														{{ item.name }}
													</Checkbox>
												</div>
											</CheckboxGroup>
										</FormItem>
									</Col>
								</Row>
								<Row>
									<Col span="24">
										<FormItem label="备注" prop="remark" style="width: 100%;">
											<Input v-model="formData.remark" maxlength="125"
													type="textarea" show-word-limit :autosize="{ minRows: 4, maxRows: 5 }"
													placeholder="请输入" />
										</FormItem>
									</Col>
								</Row>
							</Form>
						</div>
					</div>
				</div>


			</div>
		</div>
		<div class="bsp-base-fotter">
			<Button @click="onCancel" style="margin-right: 10px" v-if="!approve">取消</Button>
			<Button @click="onSubmit" :loading="loading" type="primary">提交</Button>
		</div>
	</div>
  </div>
</template>

<script>
import alonelmprisonRecords from './alonelmprisonRecords.vue'
import personnelSelector from "@/components/personnel-selector"
export default {
	components: {
		personnelSelector,
		alonelmprisonRecords
	},
	props: {
		stepType: String,
		approve: String
	},
	data() {
		return {
			formData:{
				frequencyType: '1',
				jgrybm: '',
				jgryxm: '',
				isAssociatedPunishment: '0',
				punishmentMeasures: [],
			},
			takeNotes: '单独关押记录',
			punishmentList: [],
			stepList: [],
			roomList: [],
			current: 1,
			orgType: this.$store.state.common.orgType,
			loading: false,
			roomShow: false,
		}
	},
	computed: {
		chunkedFruitList() {
      const chunkSize = 5;
      return Array.from(
        { length: Math.ceil(this.punishmentList.length / chunkSize) },
        (_, i) => this.punishmentList.slice(i * chunkSize, i * chunkSize + chunkSize)
      );
    },
		// 确保punishmentMeasures始终是数组格式
		safePunishmentMeasures: {
			get() {
				if (Array.isArray(this.formData.punishmentMeasures)) {
					return this.formData.punishmentMeasures;
				}
				// 如果是字符串，转换为数组
				if (typeof this.formData.punishmentMeasures === 'string' && this.formData.punishmentMeasures) {
					return this.formData.punishmentMeasures.split(',');
				}
				return [];
			},
			set(value) {
				this.formData.punishmentMeasures = value;
			}
		},
		isPunishmentSync: {
			get() {
				return this.formData.isAssociatedPunishment === '1';
			},
			set(value) {
				this.formData.isAssociatedPunishment = value ? '1' : '0';
			}
		},
		punishmentRules() {
			return [
				{
					validator: (_rule, value, callback) => {
						if (!value || value.length === 0) {
							callback(new Error('请选择惩罚措施'));
						} else {
							callback();
						}
					},
					trigger: 'change'
				}
			];
		}
	},
	methods: {
		onCancel() {
			this.$emit('on_show_table')
		},
		roomChange(data) {
			console.log('roomChange_data', data)
			this.formData.newRoomName = data[0].roomName
			console.log('roomChange', this.formData)
		},
		onSubmit() {
			this.loading = true
			console.log('formData', this.formData)
			if(!this.formData.jgrybm)  {
				this.loading = false
				this.$Message.error('请选择用户!!')
				return
			}

			this.loading = true
			this.$refs['formData'].validate((valid) => {
				console.log(valid)
				if(!valid) {
					this.loading = false
					this.$Message.error('请填写完整!!')
					return
				}
				try {
					// 创建提交数据的副本，避免修改原始数据
					const submitData = {
						...this.formData,
						punishmentMeasures: Array.isArray(this.formData.punishmentMeasures)
							? this.formData.punishmentMeasures.join(',')
							: this.formData.punishmentMeasures
					};
					this.$store.dispatch('authPostRequest',{
						url: this.$path.aloneImprison_save,
						params: submitData
					}).then(res => {
						if(res.success) {
							console.log(res);
							this.loading = false
							this.$Message.success('操作成功！')
							this.$nextTick(() => {
								this.$emit('on_show_table')
							})
						} else {
							this.loading = false
							this.$Modal.error({
								title: '温馨提示',
								content: res.msg || '操作失败！'
							})
						}
					}).catch(err => {
						console.log(err)
						this.loading = false
							this.$Modal.error({
								title: '温馨提示',
								content: err || '操作失败！'
							})
					})
				} catch(e) {
					console.log(e)
					this.$Modal.error({
						title: '温馨提示',
						content: e || '操作失败！'
					})
				}
			})
		},
		getRoom(roomId) {
			this.$store.dispatch('authPostRequest',{
				url: this.$path.aloneImprison_get_room,
				params: {
					nowRoomId: roomId
				}
			}).then(res => {
				if(res.data) {
					this.roomList = res.data
					console.log('roomList', this.roomList)
					this.roomShow = true
				}
			})
		},
		handlePersonnelChange(personnelData, jgrybm) {
			console.log('选择的人员:', personnelData, jgrybm)
			if(personnelData && personnelData.jgrybm) {
				this.formData.jgrybm = personnelData.jgrybm
				this.formData.jgryxm = personnelData.xm || ''
				this.getAloneImprisonHistory(personnelData.jgrybm)

				// 安全检查监室号
				if(personnelData.jsh) {
					this.getRoom(personnelData.jsh)
				}

				this.formData = {
					...this.formData,
					jgrybm: personnelData.jgrybm,
					oldRoomId: personnelData.jsh || '',
					oldRoomName: personnelData.roomName || '',
				}
			} else {
				this.$Modal.error({
					title: '温馨提示',
					content: '该监管人员编码为空'
				})
			}
		},
		getAloneImprisonHistory(jgrybm) {
			this.$store.dispatch('authGetRequest',{
				url: this.$path.aloneImprison_get_by_jgrybm,
				params: {
					jgrybm: jgrybm
				}
			}).then(res => {
				if(res.data.list) {
					this.stepList = res.data.list
				}
			})
		},

	  getPunishmentList() {
			this.$store.dispatch('authGetRequest',{
				url: `/bsp-com/static/dic/acp/ZD_GJCFNR.js`
			}).then(res => {
        let punishment = eval("(" + res + ")");
        this.punishmentList = punishment();
			})
	  }
	},
	created() {
	},
	mounted() {
		this.getPunishmentList()
	}
}
</script>

<style lang="less" scoped>
.keyperson-content{
	width: 100%;
	height: 100%;
	display: flex;
	.keyperson-content-left{
		width: 27%;
		// height: 100%;
		min-height: 680px;
		border-right: 1px solid #f1efef;
		padding: 12px;
		box-sizing: border-box;
		// background: orangered;
		.selectPerson{
			margin-bottom: 15px;
		}
	}
	.keyperson-content-right{
		position: relative;
		flex: 1;
		// height: 100%;
		min-height: 680px;
		padding: 20px;
		overflow: hidden;
		.keyperson-content-rightTop{
			color: #415060;
			min-height: 25rem;
			.title{
				position: relative;
				font-size: 16px;
				// color: #385572;
				font-weight: 500;
				line-height: 45px;
				height: 45px;
				padding-left: 15px;
				margin-left: 10px;
				margin-bottom: 10px;
			}
			.title::before {
				position: absolute;
				content: "";
				background-color: #087eff;
				width: 4px;
				height: 20px;
				left: 0;
				top: 50%;
				margin-top: -10px;
				// -webkit-border-radius: 3px;
				// -moz-border-radius: 3px;
				// border-radius: 3px;
				}
			}

			.operatBtn{
				width: 100%;
				height: 50px;
				line-height: 50px;
				text-align: right;
				background: #fff;
				position: absolute;
				bottom: 0px;
				right: 15px;
				z-index: 999;
			}
	}
}
.bsp-base-form .ivu-form .ivu-form-item-label{
	color: #b9c0c8;
}
div.v-selectpage div.sp-input-container div.sp-clear i{
	display: none;
}
.ryxx-row .ivu-col{
    font-size: 16px;
    text-align: center;
    border:1px solid #fff;
    line-height: 39px;
}
.ryxx-row .ivu-col:nth-of-type(2n){
   background: #f9f9f9;
}
.ryxx-row .ivu-col:nth-of-type(2n+1){
   background: #e4eefc;
}
.ryxx-rows .ivu-col{
	font-size: 16px;
    border:1px solid #fff;
    line-height: 39px;
}
.ryxx-rows .ivu-col:nth-of-type(2n){
   background: #f9f9f9;
   text-align: left;
   padding-left: 10px;
}
.ryxx-rows .ivu-col:nth-of-type(2n+1){
   background: #e4eefc;
   text-align: center;
}
/deep/.ivu-steps .ivu-steps-head-inner > .ivu-steps-icon.ivu-icon{
	font-size: 16px !important;
}
.ivu-steps-vertical{
	height: 300px !important;
	overflow: auto !important;
}
/deep/.ivu-steps-item.ivu-steps-status-process .ivu-steps-content{
	color: #415060 !important;
}
/deep/.ivu-steps-item.ivu-steps-status-process .ivu-steps-title{
	color: #415060 !important;
}
.disabled {
  color: #999;
  cursor: not-allowed;
  pointer-events: none; /* 禁止所有鼠标事件 */
}
.checkbox-row {
  display: flex;
  margin-bottom: 12px;
}
.checkbox-item {
  flex: 0 0 20%;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
