<template>
  <!-- <com-module-record> -->
  <div class="bsp-list-form-container" style="height: 95%;width: 98%;background: #fff;">
    <div class="bsp-list-container DataGrid-BOX" style="height: 100%;">
      <Form v-model="searchItem" class="formbox-simple">
        <Row>
          <Col span="8">
            <FormItem label="监区" prop="area_name">
              <Input style="width: 80%;" v-model="searchItem.area_name"  />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="监室号" prop="room_name">
             <Input style="width: 80%;" v-model="searchItem.room_name" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="监室类型" prop="room_type">
             <Input style="width: 80%;" v-model="searchItem.room_type" />
            </FormItem>
          </Col>
        </Row>
        <Row>
            <div style="text-align: center;margin: auto;"><Button type="primary" style="margin-right: 16px;" @click="search"> 查询</Button><Button type="default" @click="reset"> 重置</Button></div>
        </Row>
      </Form>
      <div style="display: flex;justify-content: space-between;margin-bottom: 10px;">
        <Button type="primary"> 导出</Button>
        <div style="display: flex;margin-right: 0px;">
               <div class="card-method-change first-card" @click="showCard('card')"
                 :class="{'is-active':showMethod==='card'}">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16">
                <g transform="translate(-1782.292 -8)">
                  <path
                    class="a" d="M1785,24V17h7v7Zm0-16h7v7h-7Zm-9,9h7v7h-7Zm0-9h7v7h-7Z"
                    transform="translate(6.292)"
                    :style="{
                      fill:showMethod==='card'?'#317ff5':'#7A90CC',
                     'fill-rule':'evenodd'
                    }"
                  />
                </g>
              </svg>
            </div>
            <div class="card-method-change" @click="showCard('table')" :class="{'is-active':showMethod==='table'}">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16">
                <path
                  class="a" d="M1829,23V21h11v2Zm0-8h11v2h-11Zm0-6h11v2h-11Zm-5,11h4v4h-4Zm0-6h4v4h-4Zm0-6h4v4h-4Z"
                  transform="translate(-1824 -8)"
                  :style="{
                    fill:showMethod==='table'?'#317ff5':'#7A90CC',
                    'fill-rule':'evenodd'
                  }"
                />
              </svg>
            </div>
        </div>
      </div>
      <div class="com-table-wrapper" style="height: 80%;">
        <!-- 卡片 -->
            <div class="flex-box" style="height: 90%;" v-if="showMethod=='card'">
              <comRoomCard v-show="tableData.length > 0" style="height: 100px;" v-for="(ele,index) in tableData" @toPageDetail="toPageDetail(ele)" :key="index" class="card" :content='ele' size="large">
              </comRoomCard>
              <noData v-if="tableData.length==0" style="height: 100px;"/>
            </div>
            <div class="pageWrap" style="text-align: right;" v-if="showMethod=='card'">
              <Page :total="total" class="pageWrap" size="small" show-elevator show-sizer  show-total :page-size="page.pageSize" @on-prev="getNo" @on-next="getNo" :current="page.pageNo"  @on-change="getNo" @on-page-size-change="getSize" />
            </div>
        <!-- 表格 -->
        <rs-DataGrid ref="grid" :funcMark="funcMark" v-if="funcMark && showMethod=='table'" :params="searchItem" >
          <!--详情操作-->
          <template v-slot:ysydlb:detail="{ oper, row }">
            <Button type="primary"  @click="toPageDetail(row)">
              {{ oper.name }}
            </Button>
          </template>
        </rs-DataGrid>
        <!-- <ui-table slot="table" full :columns="tableHeaders" :data="tableData" >
            <template slot="operate"   slot-scope="{row}" >
              <span class="com-table-btn"  @click="toPageDetail(row)">查看</span>
            </template>
          </ui-table>
          <div slot="card" class="com-card-box">
            <com-room-card
              v-for="(item, index) in tableData"
              :key="index"
              :content="item"
              size="large"
              class="pointer"
              @on-change="toPageDetail(item)"
            >
              <span slot="type" v-if="item.policeTag">{{item.policeTag}}</span>
            </com-room-card>
          </div> -->
      </div>
    </div>
  </div>
  <!-- </com-module-record> -->
</template>

<script>
// import {getBranchPrison} from "@/axios/zhjgBranchWork";
// import { findRoomByWarder, findByTypeCode, findBySquadronIdPageList } from "@/axios/zhjgBasicBusiness";
import noData from "@/components/noData/index.vue"
import comRoomCard from "@/components/comCard/comRoomCard.vue"
import { sDataGrid } from "sd-data-grid";
import cardTable from "./card-table.vue";
import { mapActions } from 'vuex'
export default {
  name: "roomArchiveHome",
  components: { sDataGrid, cardTable,comRoomCard,noData },
  data() {
    return {
      funcMark:'ysydlb',
      showMethod:'card',
      tableData: [],
      searchItem: {},
      unitOptions: [],
      typeOptions: [],
      prisonId: this.$store.state.common.prisonId,
      userId: `${this.$store.state.common.userid}`,
      total:0,
      page:{
        pageNo:1,
        pageSize:10
      }
    };
  },
  created() {
    // if (!this.$hasAuth("YSYD_EDITPRISON")) {
    //   this.searchItem.prisonId = this.prisonId;
    // } else {
    //   this.getPrisonList();
    // }
    this.getGridData()
  },

  methods: {
    ...mapActions(["postRequest", "authGetRequest", "authPostRequest"]),
    showCard(name) {
      this.showMethod = name
      this.search()
    },
    search(){
      this.funcMark=''
      if(this.showMethod=='card'){
       this.getGridData()
      }else{
        setTimeout(()=>{
        this.funcMark='ysydlb'
        },1000)
      }
    },
    reset(){
      this.searchItem={}
      this.funcMark=''
      this.$set(this.page, 'pageNo', 1)
      this.$set(this.page, 'pageSize', 10)
      if(this.showMethod=='card'){
       this.getGridData()
      }else{
        setTimeout(()=>{
        this.funcMark='ysydlb'
        },1000)
      }
    },
    getNo (pageNo) {
      // console.log(this.page,pageNo,'search')
      this.$set(this.page, 'pageNo', pageNo)
      this.getGridData()
    },
    getSize (pageSize) {
      // console.log(this.page,'search')
      this.$set(this.page, 'pageSize', pageSize)
      this.getGridData()
    },
    getGridData() {
      let condis=''
      for(let i in this.searchItem){
        condis+=`{"name":"${i}","op":"like","value":"${this.searchItem[i]}","valueType":"string"},`
      }
      let params={ modelId: "ysydlb",
        condis: '['+condis+']'  //[{"name":"area_name","op":"like","value":"1221","valueType":"string"}]
       }
       console.log(condis)
      Object.assign(params,this.page)
      this.postRequest({
        url: this.$path.get_query_grid,
        params: params,
      }).then((res) => {
        if (res.success) {
            this.$set(this,'tableData',res.rows ? res.rows : [])
            this.$set(this,'total',res.total)
        }
      });
    },
    getUnit(params) {
      params.prisonId = params.prisonId || this.prisonId;
      return findBySquadronIdPageList(params);
    },
    getPrisonList() {
      getBranchPrison().then((res) => {
        this.unitOptions = res.data;
      });
    },
    getTypeList() {
      findByTypeCode("C_ZXGJSLX").then((res) => {
        this.typeOptions = res.data || [];
      });
    },
    operate(params) {
      return findRoomByWarder(params).then((res) => {
        let rows = res.data.rows || [];
        rows.forEach((item) => {
          let name = [];
          let id = [];
          let assistPolice = item.assistPolice || [];
          assistPolice.map((item) => {
            name.push(item.name);
            id.push(`${item.policeId}`);
          });
          item.assistPoliceName = name.join(",");
          item.policeTag =
            `${item.policeId}` === this.userId
              ? "主管"
              : id.includes(this.userId)
              ? "协管"
              : "";
        });
        return res;
      });
    },
    toPageDetail(row) {
      console.log(row,'12212')
      // let name = this.$route.name.includes("Journal")
      //   ? "roomJournalDetail"
      //   : "roomArchiveDetail";
      this.$router.push({
        path:'/monitor/roomArchiveDetail',
        query: {
          id: row.id,
          roomName:row.room_name,
          param: this.searchItem,
        },
      });
    },
  },
};
</script>
<style scoped lang="less">
.flex-box{
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    max-height: calc(90%); /* 为分页控件预留空间 */
    min-height: 330px; /* 确保最小高度 */
    overflow-y: auto;
    // flex-direction: column; /* 默认就是从上到下 */
    .card{
        margin:0 16px 16px 0;
        width: 19.1% ;
    }
    .card:nth-child(5n){
        margin-right: 0 !important;
    }
}
/deep/ .formbox-simple{
    background: #f7f9fc;
    padding: 20px 20px 10px;
    margin-bottom: 10px;
    overflow: hidden;
    border: 1px solid #cee0f0;
}

.card-method {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.first-card {
  border-radius: 4px 0 0 4px !important;
  margin-left: 12px !important;
}

.card-method-change:last-of-type {
  border-radius: 0 4px 4px 0;
  border-left: none;
}

.card-method-change.is-active {
  border: 1px solid #317FF5;
  background: #F0F4FF;
}

.card-method-change {
  width: 40px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #cee0f0;
  padding: 5px 12px;

  &:hover {
    border: 1px solid #317FF5;
  }
}
</style>
